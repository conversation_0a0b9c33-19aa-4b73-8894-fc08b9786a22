#include <kbsp.h>

#include "xparameters.h"
#include "xiicps.h"

struct iic_struct{
	XIicPs Instance;
	T_ULONG BaseAddr;
};

static struct iic_struct g_iic[XPAR_XIICPS_NUM_INSTANCES] = {
	{.BaseAddr = XPAR_XIICPS_0_BASEADDR}
};

T_BOOL kbspRawIICInit(T_UBYTE index, T_UDWORD clk_rate)
{
	XIicPs_Config *Config;
	int Status;
	if(index >= XPAR_XIICPS_NUM_INSTANCES)
	{
		return FALSE;
	}
	Config = XIicPs_LookupConfig(g_iic[index].BaseAddr);
	if (NULL == Config)
	{
		return FALSE;
	}
	Status = XIicPs_CfgInitialize(&g_iic[index].Instance, Config, Config->BaseAddress);
	if (Status != XST_SUCCESS) {
		return FALSE;
	}

	/*
	 * Perform a self-test to ensure that the hardware was built correctly.
	 */
	Status = XIicPs_SelfTest(&g_iic[index].Instance);
	if (Status != XST_SUCCESS) {
		return FALSE;
	}

	/*
	 * Set the IIC serial clock rate.
	 */
	XIicPs_SetSClk(&g_iic[index].Instance, clk_rate);

	return TRUE;
}

T_BOOL kbspRawIICSend(T_UBYTE index, T_UBYTE slave_addr, T_UBYTE * buff, T_UBYTE size)
{
	int Status;
	while (XIicPs_BusIsBusy(&g_iic[index].Instance)) {
		/* NOP */
	}
	Status = XIicPs_MasterSendPolled(&g_iic[index].Instance, buff,
			size, slave_addr);
	if (Status != XST_SUCCESS) {
		return FALSE;
	}
	return TRUE;
}

T_BOOL kbspRawIICRecv(T_UBYTE index, T_UBYTE slave_addr, T_UBYTE * buff, T_UBYTE size)
{
	int Status;
	while (XIicPs_BusIsBusy(&g_iic[index].Instance)) {
		/* NOP */
	}
	Status = XIicPs_MasterRecvPolled(&g_iic[index].Instance, buff,
			size, slave_addr);
	if (Status != XST_SUCCESS) {
		return FALSE;
	}
	return TRUE;
}
