include  $(RTOS_PATH)/base.make

SRC_PATHS = $(BOARD_MAKEFILE_PATH)/resident/basic/drivers/src
SRC_FILES = ${wildcard $(patsubst %, %/*.c, $(SRC_PATHS))} ${wildcard $(patsubst %, %/*.S, $(SRC_PATHS))} 

SRC_OBJS = $(patsubst %.S, %.o, $(patsubst %.c, %.o, $(SRC_FILES)))

EXTRA_CFLAGS = $(DEFAULT_SEARCH_PATH)

CFLAGS := $(CFLAGS) -D$(BOARD_TYPE)
CFLAGS += -I$(BOARD_MAKEFILE_PATH)/resident/basic/drivers/include

VPATH = ${SRC_PATHS}


all: ${SRC_OBJS}
	for L_OBJ in $(SRC_OBJS); do $(BIN_PATH)/echo $$L_OBJ >> $$BOARD_MAKEFILE_PATH/resident/basic/prjObjs.lst;	done	
	
clean:
	rm -rf  *.o

include $(RTOS_PATH)/Rules.make
