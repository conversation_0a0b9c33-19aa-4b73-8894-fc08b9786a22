

/*
 * @file锟斤拷 kbspRawFlash.c
 * @brief锟斤拷
 *	    <li>实锟斤拷目锟斤拷锟紽LASH锟斤拷锟斤拷锟斤拷锟斤拷FLASH锟斤拷32bits S29GL512锟斤拷伞锟�/li>
 * @implements锟斤拷DB.2.2
 */

/* @<MODULE     */
/************************头 锟斤拷 锟斤拷******************************/

/* @<MOD_HEAD   */
#include <vmkTypes.h>
#include <kbspRawFlash.h>
#include <string.h>

#ifndef SDT
#include <xparameters.h>	/* SDK generated parameters */
#endif
#include <xqspips.h>		/* QSPI device driver */

/* @MOD_HEAD>   */

/************************锟斤拷 锟斤拷 锟斤拷******************************/

#ifndef SDT
#define QSPI_DEVICE_ID		XPAR_XQSPIPS_0_DEVICE_ID
#else
#define QSPI_DEVICE_ID		XPAR_XQSPIPS_0_BASEADDR
#endif

#define DATA_OFFSET		4 /* Start of Data for Read/Write */

#define WRITE_CMD			0x02
#define QUAD_READ_CMD		0x6B

/************************锟斤拷锟酵讹拷锟斤拷******************************/
struct QspiFlash
{
	XQspiPs QspiInstance;
	uint32_t FlashSize;
	u8 ReadBuffer[8192];
	u8 WriteBuffer[8192];
};
/************************锟解部锟斤拷锟斤拷******************************/

#ifndef SDT
int QspiG128FlashInit(XQspiPs *QspiInstancePtr, u16 QspiDeviceId);
#else
int QspiG128FlashInit(XQspiPs *QspiInstancePtr, UINTPTR BaseAddress);
#endif

void FlashErase(XQspiPs *QspiPtr, u32 Address, u32 ByteCount, u8 *WriteBfrPtr);

void FlashWrite(XQspiPs *QspiPtr, u32 Address, u32 ByteCount, u8 Command,
				u8 *WriteBfrPtr);

void FlashRead(XQspiPs *QspiPtr, u32 Address, u32 ByteCount, u8 Command,
				u8 *WriteBfrPtr, u8 *ReadBfrPtr);
u32 FlashGetPageSize(void);
u32 FlashGetSize(void);

T_WORD kbspRawFlashRead(T_UWORD uwAddr,T_CHAR *bpBuf,T_WORD uwSize);

/************************前锟斤拷锟斤拷锟斤拷******************************/

struct QspiFlash g_Flash;

/**
 * @brief:
 *    flash锟斤拷始锟斤拷锟斤拷锟斤拷锟斤拷
 * @return:
 *    锟斤拷
 * @tracedREQ: RB.2.2.1
 * @implements: DB.2.2.1
 */
T_VOID kbspRawFlashInit(T_VOID)
{
	int status;
	status = QspiG128FlashInit(&g_Flash.QspiInstance, QSPI_DEVICE_ID);
	if(status != XST_SUCCESS)
	{
		printk("Flash Init Error\n");
		return;
	}
	printk("Flash Size %d MBytes\n", FlashGetSize() / 0x100000);
}

/*
 * @brief:
 *    flash锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷
 * @param[in]: uwAddr: 锟斤拷始锟斤拷址
 * @param[in]: size: 锟斤拷锟斤拷锟斤拷小
 * @return:
 *    TRUE: 锟斤拷锟斤拷锟缴癸拷锟斤拷
 *    FALSE: 锟斤拷锟斤拷失锟杰★拷
 * @tracedREQ: RB.2.2.2
 * @implements: DB.2.2.2
 */
T_BOOL kbspRawFlashErase(T_UWORD addr,T_UWORD size)
{
	// 锟斤拷写锟斤拷锟斤拷锟斤拷锟叫诧拷锟斤拷 锟剿达拷锟斤拷锟劫诧拷锟斤拷
//	printk("raw earse addr 0x%x size 0x%x\n", addr, size);
//	FlashErase(&g_Flash.QspiInstance, addr, size, g_Flash.WriteBuffer);
//	printk("raw earse done\n");
	return TRUE;
}
T_BOOL kbspRawFlashBlockErase(T_UWORD addr,T_UWORD size)
{
//	printk("earse addr 0x%x size 0x%x\n", addr, size);
	FlashErase(&g_Flash.QspiInstance, addr, size, g_Flash.WriteBuffer);
//	printk("earse done\n");
	return TRUE;
}

static T_WORD kbspRawFlashBlockWrite(T_UWORD addr,T_CONST T_UBYTE *bpBuf,T_UWORD size)
{
	uint32_t xfer_size;
	uint32_t remian_size = size;
	uint32_t page_size = FlashGetPageSize();
//	printk("write addr 0x%x size 0x%x\n", addr, size);
	while(remian_size)
	{
		xfer_size = remian_size > page_size? page_size : remian_size;
		memcpy(&g_Flash.WriteBuffer[DATA_OFFSET], &bpBuf[size - remian_size], xfer_size);
		FlashWrite(&g_Flash.QspiInstance, addr + (size - remian_size), xfer_size, WRITE_CMD, g_Flash.WriteBuffer);
		remian_size -= xfer_size;
	}
//	printk("write done\n");
	return size;
}

/**
 * @brief:
 *    锟斤拷指锟斤拷锟斤拷小锟斤拷锟斤拷锟斤拷写锟斤拷flash锟斤拷指锟斤拷位锟矫★拷
 * @param[in]: bpBuf: 指锟诫，指锟斤拷写锟斤拷锟斤拷锟斤拷
 * @param[in]: size: 锟斤拷锟捷筹拷锟斤拷
 * @param[out]: addr: 锟斤拷始锟斤拷址
 * @return:
 *    -FLASH_INVALID_SPACE: 锟斤拷址锟秸硷拷锟斤拷效锟斤拷
 *    -FLASH_OPERATE_BUSY锟斤拷锟借备忙锟斤拷
 *    -FLASH_OPERATE_FAIL: 写失锟杰★拷
 *    size锟斤拷锟斤拷锟斤拷全锟斤拷写锟斤拷
 * @tracedREQ: RB.2.2.3
 * @implements: DB.2.2.3
 */
T_WORD kbspRawFlashWrite(T_UWORD addr,T_CONST T_UBYTE *bpBuf,T_UWORD size)
{
	T_UWORD flash_start_addr = kbspRawFlashQureyStartAddr();
	T_UWORD flash_size = kbspRawFlashQureySize();
	T_UWORD sector_size = kbspRawFlashQureySectorSize(); // 4K
	T_UWORD page_size = FlashGetPageSize(); // 256 bytes

	T_UWORD current_addr = addr;
	T_UWORD remaining_size = size;
	T_CONST T_UBYTE *current_buf = bpBuf;

	// 锟斤拷锟斤拷锟斤拷锟�
	if (bpBuf == NULL || size == 0) {
		return -(T_WORD)FLASH_OPERATE_FAIL;
	}

	// 锟斤拷址锟斤拷围锟斤拷锟�
	if (addr < flash_start_addr || (addr + size) > (flash_start_addr + flash_size)) {
		return -(T_WORD)FLASH_INVALID_SPACE;
	}

//	printk("stream write addr 0x%x size 0x%x\n", addr, size);

	// 锟斤拷锟街节达拷锟斤拷锟斤拷确锟斤拷只锟杰达拷1写为0
	while (remaining_size > 0) {
		T_UWORD sector_start = (current_addr / sector_size) * sector_size;
		T_UWORD sector_offset = current_addr - sector_start;
		T_UWORD bytes_in_sector = sector_size - sector_offset;
		T_UWORD bytes_to_process = (remaining_size < bytes_in_sector) ? remaining_size : bytes_in_sector;

		// 锟斤拷取锟斤拷前锟斤拷锟斤拷锟斤拷锟斤拷
		static T_UBYTE sector_buffer[4096];
		if (kbspRawFlashRead(sector_start, (T_CHAR*)sector_buffer, sector_size) != sector_size) {
			return -(T_WORD)FLASH_READ_FAIL;
		}

		// 锟斤拷锟斤拷欠锟斤拷锟揭拷锟斤拷锟斤拷锟斤拷欠锟斤拷写锟�写为1锟侥诧拷锟斤拷锟斤拷
		T_BOOL need_erase = FALSE;
		T_UWORD i;
		for (i = 0; i < bytes_to_process; i++) {
			T_UBYTE old_data = sector_buffer[sector_offset + i];
			T_UBYTE new_data = current_buf[i];
			// 锟斤拷锟斤拷欠锟斤拷锟轿伙拷锟�锟斤拷为1锟斤拷锟斤拷锟斤拷要锟斤拷锟斤拷锟斤拷
			// 锟斤拷锟�new_data 锟斤拷锟斤拷位为1锟斤拷锟斤拷 old_data 锟叫讹拷应位为0锟斤拷锟斤拷锟斤拷要锟斤拷锟斤拷
			if ((old_data | new_data) != old_data) {
				need_erase = TRUE;
				break;
			}
		}

		if (need_erase) {
			// 锟斤拷要锟斤拷锟斤拷锟斤拷锟饺革拷锟铰伙拷锟斤拷锟斤拷锟叫碉拷锟斤拷锟斤拷
			for (i = 0; i < bytes_to_process; i++) {
				sector_buffer[sector_offset + i] = current_buf[i];
			}

			// 锟斤拷锟斤拷锟斤拷锟斤拷
//			printk("earse addr 0x%x size 0x%x\n", sector_start, sector_size);
			FlashErase(&g_Flash.QspiInstance, sector_start, sector_size, g_Flash.WriteBuffer);

			// 写锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷
			if (kbspRawFlashBlockWrite(sector_start, sector_buffer, sector_size) != sector_size) {
				return -(T_WORD)FLASH_OPERATE_FAIL;
			}
		}
		{
			// 锟斤拷锟斤拷要锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷直锟斤拷写锟诫（只锟角达拷1写为0锟斤拷
			// 锟斤拷页写锟斤拷
			T_UWORD page_addr = current_addr;
			T_UWORD page_remaining = bytes_to_process;
			T_CONST T_UBYTE *page_buf = current_buf;

			while (page_remaining > 0) {
				T_UWORD page_start = (page_addr / page_size) * page_size;
				T_UWORD page_offset = page_addr - page_start;
				T_UWORD bytes_in_page = page_size - page_offset;
				T_UWORD bytes_to_write = (page_remaining < bytes_in_page) ? page_remaining : bytes_in_page;

				if (page_offset == 0 && bytes_to_write == page_size) {
					// 锟斤拷页写锟斤拷
					memcpy(&g_Flash.WriteBuffer[DATA_OFFSET], page_buf, page_size);
					FlashWrite(&g_Flash.QspiInstance, page_start, page_size, WRITE_CMD, g_Flash.WriteBuffer);
				} else {
					// 锟斤拷锟斤拷页写锟诫，锟斤拷要锟斤拷-锟睫革拷-写
					static T_UBYTE page_buffer[256];
					if (kbspRawFlashRead(page_start, (T_CHAR*)page_buffer, page_size) != page_size) {
						return -(T_WORD)FLASH_READ_FAIL;
					}

					// 锟斤拷锟斤拷页锟斤拷锟斤拷锟斤拷
					for (i = 0; i < bytes_to_write; i++) {
						page_buffer[page_offset + i] = page_buf[i]; // 只锟杰达拷1写为0
					}

					// 写锟斤拷页
					memcpy(&g_Flash.WriteBuffer[DATA_OFFSET], page_buffer, page_size);
					FlashWrite(&g_Flash.QspiInstance, page_start, page_size, WRITE_CMD, g_Flash.WriteBuffer);
				}

				page_addr += bytes_to_write;
				page_buf += bytes_to_write;
				page_remaining -= bytes_to_write;
			}
		}

		current_addr += bytes_to_process;
		current_buf += bytes_to_process;
		remaining_size -= bytes_to_process;
	}

//	printk("stream write done\n");
	return size;
}


/**
 * @brief
 *	锟斤拷锟斤拷:
 *		锟斤拷flash锟叫讹拷取锟斤拷锟捷★拷
 *
 *  实锟斤拷锟斤拷锟捷ｏ拷
 *		锟斤拷锟斤拷指锟斤拷锟侥筹拷锟饺猴拷位锟斤拷,锟斤拷指锟斤拷锟斤拷flash位锟矫讹拷锟斤拷锟斤拷锟捷★拷
 *
 * @param[in] uwAddr:锟斤拷始锟斤拷址
 * @param[in] bpBuf:指锟诫，锟斤拷取锟斤拷锟斤拷锟捷凤拷锟诫到锟矫碉拷址锟侥伙拷锟斤拷
 * @param[in] uwSize:锟斤拷取锟斤拷锟捷筹拷锟斤拷
 *
 * @return 锟缴癸拷锟斤拷锟斤拷1锟斤拷锟斤拷锟津，凤拷锟斤拷0锟斤拷
 */
T_WORD kbspRawFlashRead(T_UWORD uwAddr,T_CHAR *bpBuf,T_WORD uwSize)
{
	uint32_t xfer_size;
	uint32_t remian_size = uwSize;
	while(remian_size)
	{
		xfer_size = remian_size > 4096? 4096 : remian_size;
		FlashRead(&g_Flash.QspiInstance, uwAddr + (uwSize - remian_size), xfer_size, QUAD_READ_CMD,
						g_Flash.WriteBuffer, g_Flash.ReadBuffer);
		memcpy(&bpBuf[uwSize - remian_size], &g_Flash.ReadBuffer[0], xfer_size);
		remian_size -= xfer_size;
	}
	return uwSize;
}


/**
 * @brief:
 *    锟斤拷锟斤拷flash锟斤拷锟斤拷效锟斤拷始锟斤拷址锟斤拷
 * @return:
 *    flash锟斤拷锟斤拷效锟斤拷址锟斤拷
 * @tracedREQ: RB.2.2.4
 * @implements: DB.2.2.4
 */ 
T_UWORD kbspRawFlashQureyStartAddr(T_VOID)
{
	return(0);
}

/**
 * @brief:
 *    锟斤拷锟斤拷flash锟斤拷锟斤拷效锟斤拷小锟斤拷
 * @return:
 *    flash锟斤拷锟斤拷效锟斤拷小锟斤拷
 * @tracedREQ: RB.2.2.5
 * @implements: DB.2.2.5
 */
T_UWORD kbspRawFlashQureySize(T_VOID)
{
	return FlashGetSize();
}

/**
 * @brief:
 *    锟斤拷锟斤拷flash锟斤拷每锟斤拷锟斤拷锟斤拷锟侥达拷小锟斤拷
 * @return:
 *    flash锟斤拷每锟斤拷锟斤拷锟斤拷锟侥达拷小锟斤拷
 * @tracedREQ: RB.2.2.6
 * @implements: DB.2.2.6
 */
T_UWORD kbspRawFlashQureySectorSize(T_VOID)
{
	return 4096;
}	

/**
 * @brief:
 *    锟斤拷锟斤拷校锟斤拷flash锟斤拷锟斤拷效锟斤拷始锟斤拷址锟斤拷
 * @return:
 *    flash锟斤拷锟斤拷效锟斤拷址锟斤拷
 * @tracedREQ: RB.2.2
 * @implements: DB.2.2.7
 */
T_UWORD kbspRawVerifyFlashQureyStartAddr(T_VOID)
{
	return(0);
}

/**
 * @brief:
 *    锟斤拷锟斤拷校锟斤拷flash锟斤拷锟斤拷效锟斤拷小锟斤拷
 * @return:
 *    flash锟斤拷锟斤拷效锟斤拷小锟斤拷
 * @tracedREQ: RB.2.2
 * @implements: DB.2.2.8
 */
T_UWORD kbspRawVerifyFlashQureySize(T_VOID)
{
	return FlashGetSize();
}

/**
 * @brief:
 *    锟斤拷锟斤拷校锟斤拷flash锟斤拷每锟斤拷锟斤拷锟斤拷锟侥达拷小锟斤拷
 * @return:
 *    flash锟斤拷每锟斤拷锟斤拷锟斤拷锟侥达拷小锟斤拷
 * @tracedREQ: RB.2.2
 * @implements: DB.2.2.9
 */
T_UWORD kbspRawVerifyFlashQureySectorSize(T_VOID)
{
	return 4096;
}


