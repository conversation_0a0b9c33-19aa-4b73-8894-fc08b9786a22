#include <kbsp.h>

#include "xparameters.h"
#include "xtmrctr.h"

struct timer_struct{
	XTmrCtr Instance;
	T_ULONG BaseAddr;
};
struct timer_struct g_timer[XPAR_XTMRCTR_NUM_INSTANCES] =
{
	{.BaseAddr = XPAR_XTMRCTR_0_BASEADDR},
	{.BaseAddr = XPAR_XTMRCTR_1_BASEADDR},
	{.BaseAddr = XPAR_XTMRCTR_2_BASEADDR},
	{.BaseAddr = XPAR_XTMRCTR_3_BASEADDR},
	{.BaseAddr = XPAR_XTMRCTR_4_BASEADDR},
	{.BaseAddr = XPAR_XTMRCTR_5_BASEADDR},
	{.BaseAddr = XPAR_XTMRCTR_6_BASEADDR},
	{.BaseAddr = XPAR_XTMRCTR_7_BASEADDR},
	{.BaseAddr = XPAR_XTMRCTR_8_BASEADDR},
	{.BaseAddr = XPAR_XTMRCTR_9_BASEADDR},
	{.BaseAddr = XPAR_XTMRCTR_10_BASEADDR},
};

T_UBYTE kbspRawPWMChannelCount(T_VOID)
{
	return XPAR_XTMRCTR_NUM_INSTANCES;
}

T_BOOL kbspRawPWMInit(T_UBYTE index)
{
	int Status;
	if(index >= XPAR_XTMRCTR_NUM_INSTANCES)
	{
		return FALSE;
	}

	Status = XTmrCtr_Initialize(&g_timer[index].Instance, g_timer[index].BaseAddr);
	if (Status != XST_SUCCESS) {
		printk("Timer Initialize Failed\n");
		return FALSE;
	}
	Status = XTmrCtr_SelfTest(&g_timer[index].Instance, 0);
	if (Status != XST_SUCCESS) {
		printk("Timer 0 not Enable\n");
		return FALSE;
	}
	Status = XTmrCtr_SelfTest(&g_timer[index].Instance, 1);
	if (Status != XST_SUCCESS) {
		printk("Timer 1 not Enable\n");
		return FALSE;
	}
	XTmrCtr_SetOptions(&g_timer[index].Instance, 0, XTC_AUTO_RELOAD_OPTION | XTC_DOWN_COUNT_OPTION);
	XTmrCtr_SetOptions(&g_timer[index].Instance, 1, XTC_AUTO_RELOAD_OPTION | XTC_DOWN_COUNT_OPTION);
	return TRUE;
}

T_BOOL kbspRawPWMEnable(T_UBYTE index)
{
	if(index >= XPAR_XTMRCTR_NUM_INSTANCES)
	{
		return FALSE;
	}
	XTmrCtr_PwmEnable(&g_timer[index].Instance);
	return TRUE;
}

T_BOOL kbspRawPWMDisable(T_UBYTE index)
{
	if(index >= XPAR_XTMRCTR_NUM_INSTANCES)
	{
		return FALSE;
	}
	XTmrCtr_PwmDisable(&g_timer[index].Instance);
	return TRUE;
}

T_UBYTE kbspRawConfigurePWM(T_UBYTE index, T_UWORD period_us, T_UWORD high_us)
{
	if(index >= XPAR_XTMRCTR_NUM_INSTANCES)
	{
		return 0;
	}
	return XTmrCtr_PwmConfigure(&g_timer[index].Instance, period_us * 1000, high_us * 1000);
}
