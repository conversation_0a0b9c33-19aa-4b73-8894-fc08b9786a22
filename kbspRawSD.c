#include "xsdps.h"
#include "kbspTypes.h"
struct SDInstance
{
	XSdPs Instance;
	T_ULONG BaseAddr;
} g_sd[] =
{
		{.BaseAddr = XPAR_XSDPS_0_BASEADDR},
		{.BaseAddr = XPAR_XSDPS_1_BASEADDR},
};

T_BOOL kbspRawSDInit(T_UBYTE minor)
{
	XSdPs_Config *SdConfig;
	int Status;
	if(minor > 1) return FALSE;
	SdConfig = XSdPs_LookupConfig(g_sd[minor].BaseAddr);

	if (NULL == SdConfig) {
		return FALSE;
	}

	Status = XSdPs_CfgInitialize(&g_sd[minor].Instance, SdConfig,
					 SdConfig->BaseAddress);
	if (Status != XST_SUCCESS) {
		return FALSE;
	}

	Status = XSdPs_CardInitialize(&g_sd[minor].Instance);
	if (Status != XST_SUCCESS) {
		return FALSE;
	}
	return TRUE;
}

T_WORD kbspRawSDBlockWrite(T_UBYTE minor, T_UWORD sector, T_UWORD blkcnt, T_UBYTE * buff)
{
	int Status;
	if(minor > 1) return 0;
	if (!(g_sd[minor].Instance.HCS)) {
	sector *= XSDPS_BLK_SIZE_512_MASK;
	}
	Status = XSdPs_WritePolled(&g_sd[minor].Instance, sector, blkcnt,
			buff);
	if (Status != XST_SUCCESS) {
		return 0;
	}
	return blkcnt;
}

T_WORD kbspRawSDBlockRead(T_UBYTE minor, T_UWORD sector, T_UWORD blkcnt, T_UBYTE * buff)
{
	int Status;
	if(minor > 1) return 0;
	if (!(g_sd[minor].Instance.HCS)) {
	sector *= XSDPS_BLK_SIZE_512_MASK;
	}
	Status = XSdPs_ReadPolled(&g_sd[minor].Instance, sector, blkcnt,
			buff);
	if (Status != XST_SUCCESS) {
		return 0;
	}
	return blkcnt;
}

T_UWORD kbspRawSDGetSectorCount(T_UBYTE minor)
{
	if(minor > 1) return 0;
	return g_sd[minor].Instance.SectorCount;
}
