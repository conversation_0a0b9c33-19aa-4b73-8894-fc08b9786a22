

/*
 * @file锟斤拷 kbspRawIntr.c
 * @brief锟斤拷
 *	    <li>实锟斤拷锟叫断匡拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷</li>
 * @implements: DB.2.4
 */

/* @<MODULE     */

/************************头 锟斤拷 锟斤拷******************************/

/* @<MOD_HEAD   */
#include <kbspRawIntr.h>
#include "vmkTypes.h"
#include "vmkUtils.h"
#include <kbspTypes.h>
#include <kbspArchOp.h>
#include <stdio.h>
#include <vmkHal.h>
#include <xscugic.h>
#include <xparameters_ps.h>
/************************锟斤拷 锟斤拷 锟斤拷******************************/
/************************锟斤拷锟酵讹拷锟斤拷******************************/
/************************锟解部锟斤拷锟斤拷******************************/
/************************前锟斤拷锟斤拷锟斤拷******************************/
/************************模锟斤拷锟斤拷锟�*****************************/
T_MODULE XScuGic GicInstance;
/************************全锟街憋拷锟斤拷******************************/
/* 锟斤拷锟斤拷锟叫断猴拷 */


/************************实   锟斤拷*******************************/


/*
 * @brief
 *    锟斤拷前系统锟斤拷锟斤拷锟斤拷锟侥革拷锟斤拷锟较ｏ拷锟斤拷0锟斤拷始锟斤拷
 * @param[in]: 锟斤拷
 * @return:
 *    锟斤拷前系统锟斤拷锟叫碉拷CPU ID锟斤拷
 *
 */
T_MODULE T_UWORD kbspRawGetCurrentCPUId(T_VOID)
{
	return 0;
}


/**
* @brief
* 	   锟斤拷始锟斤拷锟叫断匡拷锟斤拷锟斤拷
* @return: 
*     锟斤拷
* @tracedREQ: RB.2.4.1
* @implements: DB.2.4.1
*/
T_VOID kbspRawInterruptInit(T_VOID)
{
	XScuGic_Config * Config;
	T_UWORD Status;
#ifndef SDT
	Config = XScuGic_LookupConfig(XPAR_XSCUGIC_0_DEVICE_ID);
#else
	Config = XScuGic_LookupConfig(XPAR_XSCUGIC_0_BASEADDR);
#endif
	Status = XScuGic_CfgInitialize(&GicInstance, Config, Config->CpuBaseAddress);
	if (Status != XST_SUCCESS)
	{
		printk("GIC Init error\n");
		return;
	}
//	printk("GIC init\n");
	return;
}

/*
 * @brief
 *    使锟斤拷指锟斤拷锟斤拷锟叫断匡拷锟斤拷锟斤拷位锟斤拷
 * @param[in]: intNum: 锟叫断号ｏ拷实锟绞凤拷围锟缴撅拷锟斤拷锟叫断匡拷锟斤拷锟斤拷确锟斤拷
 * @return: 
 *    KBSP_OK: 执锟叫成癸拷锟斤拷
 *    VMK_INVALID_INDEX: 锟叫断号达拷锟斤拷
 * @tracedREQ: RB.2.4.2
 * @implements: DB.2.4.2
 */
T_KBSP_ReturnCode kbspRawEnablePIC(UINT32 intNum)
{
	if(intNum >= IPI_INUMS_BASE)
	{
		intNum -= IPI_INUMS_BASE;
//		printk("Enable ipi %d\n", intNum);
		return KBSP_OK;
	}
	XScuGic_SetPriorityTriggerType(&GicInstance, intNum,
					       0xA0, 0x3);
	XScuGic_Enable(&GicInstance, intNum);
//	printk("Enable INT %d\n", intNum);
	return KBSP_OK;
}

/*
 * @brief
 *    锟斤拷止指锟斤拷锟斤拷锟叫断匡拷锟斤拷锟斤拷位锟斤拷
 * @param[in]: intNum: 锟叫断号ｏ拷实锟绞凤拷围锟缴撅拷锟斤拷锟叫断匡拷锟斤拷锟斤拷确锟斤拷
 * @return: 
 *    KBSP_OK: 执锟叫成癸拷锟斤拷
 *    VMK_INVALID_INDEX: 锟叫断号达拷锟斤拷
 * @tracedREQ: RB.2.4.3
 * @implements: DB.2.4.3
 */
T_KBSP_ReturnCode kbspRawDisablePIC(UINT32 intNum)
{
	if(intNum >= IPI_INUMS_BASE)
	{
		intNum -= IPI_INUMS_BASE;
		printk("Disable ipi %d\n", intNum);
		return KBSP_OK;
	}
//	printk("Disable INT %d\n", intNum);
	XScuGic_Disable(&GicInstance, intNum);

	return KBSP_OK;
}

/*
 * @brief
 *    锟斤拷止锟斤拷锟叫碉拷锟叫断匡拷锟斤拷锟斤拷位锟斤拷
 * @param[]: 锟叫讹拷使锟斤拷状态锟斤拷志
 * @return:
 * @tracedREQ: RB.2.4.3
 * @implements: DB.2.4.3
 */
T_VOID kbspRawDisableAllPIC(UINT32* msk)
{

}

/*
 * @brief
 *    锟斤拷止锟斤拷锟叫碉拷锟叫断匡拷锟斤拷锟斤拷位锟斤拷
 * @param[]: 锟叫讹拷使锟斤拷状态锟斤拷志
 * @return:
 * @tracedREQ: RB.2.4.3
 * @implements: DB.2.4.3
 */
T_VOID kbspRawResumeAllPIC(UINT32* msk)
{

}

/*
 * @brief
 *    锟斤拷锟斤拷锟斤拷前锟叫断ｏ拷锟街革拷锟叫断匡拷锟斤拷锟斤拷锟斤拷状态锟斤拷
 * @return: 
 *    锟斤拷
 * @tracedREQ: RB.2.4.4
 * @implements: DB.2.4.4
 */
T_VOID kbspRawIntTerminate(UINT32 intNum)
{
	XScuGic_CPUWriteReg(&GicInstance, XSCUGIC_EOI_OFFSET, intNum);
}

/*
 * @brief
 *    锟斤拷取锟叫断号★拷
 * @return: 
 *    锟叫断号★拷
 * @tracedREQ: RB.2.4.5
 * @implements: DB.2.4.5
 */
T_UWORD kbspRawGetVectorNum(T_VOID)
{
	return (T_UWORD)XScuGic_CPUReadReg(&GicInstance, XSCUGIC_INT_ACK_OFFSET);
}

/*
 * @brief
 *    锟斤拷取锟斤拷锟斤拷锟叫断号★拷
 * @return: 
 *    锟叫断号★拷
 * @tracedREQ: RB.2.4.9
 * @implements: DB.2.4.9
 */
T_UWORD kbspRawGetMaxVectorNumber(T_VOID)
{
	return XSCUGIC_MAX_NUM_INTR_INPUTS;
}

#ifdef CONFIG_CORE_SMP
/*
 * @brief
 *  锟斤拷锟斤拷指锟斤拷锟叫讹拷锟斤拷cpu锟斤拷锟斤拷
 * @param[in]: intNum锟斤拷锟叫断猴拷
 * @param[in]: cpus:cpu锟斤拷锟斤拷
 * @return:
 * @tracedREQ: RB.2.4.2
 * @implements: DB.2.4.2
 */
T_KBSP_ReturnCode kbspRawDirectIntrToCpu(UINT32 intNum, cpuset_t cpus)
{
	return (KBSP_OK);
}
/*
 * @brief
 *    锟斤拷始锟斤拷IPI锟叫讹拷
 * @return:
 *
 * @tracedREQ: RB.
 * @implements: DB.
 */
T_VOID kbspRawInitIPI(T_VOID)
{
}

/*
 * @brief
 *    锟斤拷锟斤拷SGI锟叫断硷拷IPI锟叫断★拷
 * @param[in] : sgi_num锟斤拷sgi锟叫断号★拷
 * @param[in] :target锟斤拷目锟侥达拷锟斤拷锟斤拷锟斤拷
 * @returns: 锟睫★拷
 */
void kbspRawSgi(unsigned int sgi_num, u_register_t target)
{
	
}


/*
* @brief
*       锟斤拷指锟斤拷锟斤拷CPUS锟斤拷锟斤拷IPI锟叫讹拷
* @param[in]  cpus: 目锟斤拷CPU锟斤拷锟斤拷,0锟斤拷示锟斤拷锟斤拷使锟杰碉拷cpu锟斤拷
* @param[in]  ipiNum: IPI锟斤拷应锟斤拷锟叫断号★拷
* @return
*	0:锟斤拷锟酵成癸拷
*	锟斤拷锟斤拷:锟斤拷锟斤拷失锟斤拷
* @implements  2012.05.24
*/
T_KBSP_ReturnCode kbspRawEmitIPI(cpuset_t cpus,INT32 ipiNum)
{
    kbspRawSgi(ipiNum,cpus);
    return (KBSP_OK);
}


/*
* @brief
* 	   锟斤拷始锟斤拷锟接猴拷锟叫断匡拷锟斤拷锟斤拷
* @return:
*     锟斤拷
*/
T_VOID kbspRawApInterruptInit(T_VOID)
{
	kbspRawInterruptInit();
}


T_VOID kbspRawGetIrqActivitySts(UINT16 vector, BOOL *activity)
{

}

#endif
